<template>
  <div v-if="isVisible" class="modal-overlay" @click="closeModal">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>{{ title }}</h3>
        <button @click="closeModal" class="close-button">✕</button>
      </div>
      <div class="modal-body">
        <component :is="getInteractiveComponent(type)" :data="data" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineComponent, h, ref } from 'vue'

interface Props {
  isVisible: boolean
  type: string
  title: string
  data?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

const closeModal = () => {
  emit('close')
}

// 通用交互组件
const DefaultInteractive = defineComponent({
  name: 'DefaultInteractive',
  props: ['data'],
  setup(props) {
    return () =>
      h('div', { class: 'default-interactive' }, [
        h('h4', '🚧 功能开发中'),
        h('p', '这个交互功能正在开发中，敬请期待！'),
        h('div', { class: 'coming-soon' }, [
          h('div', { class: 'spinner' }),
          h('span', '即将推出...'),
        ]),
      ])
  },
})

const CodeComparison = defineComponent({
  name: 'CodeComparison',
  props: ['data'],
  setup(props) {
    const examples = ref([
      {
        title: '基础示例',
        before:
          '// 传统写法\nfor (int i = 0; i < list.size(); i++) {\n    System.out.println(list.get(i));\n}',
        after: '// 现代写法\nlist.forEach(System.out::println);',
      },
    ])

    const selectedExample = ref(0)

    return () =>
      h('div', { class: 'code-comparison' }, [
        h(
          'div',
          { class: 'example-tabs' },
          examples.value.map((example, index) =>
            h(
              'button',
              {
                key: index,
                onClick: () => (selectedExample.value = index),
                class: ['tab-button', { active: selectedExample.value === index }],
              },
              example.title,
            ),
          ),
        ),
        h('div', { class: 'comparison-content' }, [
          h('div', { class: 'code-section' }, [
            h('h4', '传统方式'),
            h('pre', { class: 'code-block' }, examples.value[selectedExample.value].before),
          ]),
          h('div', { class: 'code-section' }, [
            h('h4', '现代方式'),
            h('pre', { class: 'code-block' }, examples.value[selectedExample.value].after),
          ]),
        ]),
      ])
  },
})

const ConceptDemo = defineComponent({
  name: 'ConceptDemo',
  props: ['data'],
  setup(props) {
    const step = ref(0)
    const steps = ref([
      { title: '步骤 1', description: '初始化概念', code: 'System.out.println("Hello World");' },
      {
        title: '步骤 2',
        description: '应用概念',
        code: 'List<String> list = Arrays.asList("a", "b", "c");',
      },
      { title: '步骤 3', description: '验证结果', code: 'list.forEach(System.out::println);' },
    ])

    const nextStep = () => {
      if (step.value < steps.value.length - 1) {
        step.value++
      }
    }

    const prevStep = () => {
      if (step.value > 0) {
        step.value--
      }
    }

    const resetDemo = () => {
      step.value = 0
    }

    return () =>
      h('div', { class: 'concept-demo' }, [
        h('div', { class: 'demo-header' }, [
          h('h4', `${steps.value[step.value].title}: ${steps.value[step.value].description}`),
          h('div', { class: 'step-indicator' }, `${step.value + 1} / ${steps.value.length}`),
        ]),
        h('div', { class: 'demo-content' }, [
          h('pre', { class: 'code-block' }, steps.value[step.value].code),
        ]),
        h('div', { class: 'demo-controls' }, [
          h(
            'button',
            {
              onClick: prevStep,
              disabled: step.value === 0,
              class: 'control-button',
            },
            '⬅️ 上一步',
          ),
          h(
            'button',
            {
              onClick: resetDemo,
              class: 'control-button',
            },
            '🔄 重置',
          ),
          h(
            'button',
            {
              onClick: nextStep,
              disabled: step.value === steps.value.length - 1,
              class: 'control-button',
            },
            '下一步 ➡️',
          ),
        ]),
      ])
  },
})

const InteractivePlayground = defineComponent({
  name: 'InteractivePlayground',
  props: ['data'],
  setup(props) {
    const code = ref('// 在这里编写代码\nSystem.out.println("Hello, Interactive World!");')
    const output = ref('')
    const isRunning = ref(false)

    const runCode = async () => {
      isRunning.value = true
      output.value = '正在执行...\n'

      // 模拟代码执行
      await new Promise((resolve) => setTimeout(resolve, 1000))
      output.value = 'Hello, Interactive World!\n执行完成！'

      isRunning.value = false
    }

    return () =>
      h('div', { class: 'interactive-playground' }, [
        h('h4', '💻 交互式代码编辑器'),
        h('textarea', {
          value: code.value,
          onInput: (e: any) => (code.value = e.target.value),
          class: 'code-editor',
          rows: 8,
          placeholder: '在这里编写你的代码...',
        }),
        h(
          'button',
          {
            onClick: runCode,
            disabled: isRunning.value,
            class: 'run-button',
          },
          isRunning.value ? '⏳ 运行中...' : '▶️ 运行代码',
        ),
        h('div', { class: 'result-section' }, [
          h('h4', '📤 输出结果'),
          h('pre', { class: 'result-output' }, output.value),
        ]),
      ])
  },
})

// 第1章专用组件 - 编译过程演示
const CompilationDemo = defineComponent({
  name: 'CompilationDemo',
  props: ['data'],
  setup(props) {
    const step = ref(0)
    const steps = [
      {
        title: '📝 源代码',
        content: 'HelloWorld.java',
        description: '编写Java源代码文件，包含类定义和方法',
      },
      {
        title: '🔍 词法分析',
        content: 'javac 词法解析',
        description: '编译器将源代码分解为标记(tokens)',
      },
      {
        title: '🌳 语法分析',
        content: '构建语法树',
        description: '生成抽象语法树(AST)，表示代码结构',
      },
      { title: '✅ 语义分析', content: '类型检查', description: '验证类型安全性和语义正确性' },
      {
        title: '⚙️ 字节码生成',
        content: 'HelloWorld.class',
        description: '生成平台无关的字节码文件',
      },
      { title: '📦 类加载', content: 'ClassLoader 加载', description: 'JVM加载并验证字节码文件' },
      { title: '🚀 执行', content: 'JIT + 解释执行', description: '混合执行模式运行程序' },
    ]

    const nextStep = () => {
      if (step.value < steps.length - 1) step.value++
    }

    const prevStep = () => {
      if (step.value > 0) step.value--
    }

    const reset = () => {
      step.value = 0
    }

    return () =>
      h('div', { class: 'concept-demo' }, [
        h('div', { class: 'demo-header' }, [
          h('h4', '🔧 Java编译执行过程'),
          h('span', { class: 'step-indicator' }, `步骤 ${step.value + 1}/${steps.length}`),
        ]),
        h(
          'div',
          {
            class: 'compilation-step',
            style:
              'padding: 2rem; text-align: center; background: #f8f9fa; border-radius: 8px; margin: 1rem 0;',
          },
          [
            h('h3', { style: 'color: #667eea; margin-bottom: 1rem;' }, steps[step.value].title),
            h(
              'div',
              {
                style:
                  'font-size: 1.2rem; font-weight: bold; color: #333; margin: 1rem 0; padding: 1rem; background: white; border-radius: 6px; border: 2px solid #667eea;',
              },
              steps[step.value].content,
            ),
            h('p', { style: 'color: #666; line-height: 1.6;' }, steps[step.value].description),
          ],
        ),
        h('div', { class: 'demo-controls' }, [
          h(
            'button',
            {
              onClick: prevStep,
              disabled: step.value === 0,
              class: 'control-button',
            },
            '⬅️ 上一步',
          ),
          h(
            'button',
            {
              onClick: reset,
              class: 'control-button',
            },
            '🔄 重置',
          ),
          h(
            'button',
            {
              onClick: nextStep,
              disabled: step.value === steps.length - 1,
              class: 'control-button',
            },
            '下一步 ➡️',
          ),
        ]),
      ])
  },
})

// 第1章专用组件 - JVM架构图
const JVMArchitecture = defineComponent({
  name: 'JVMArchitecture',
  props: ['data'],
  setup(props) {
    const selectedArea = ref('class-loader')
    const areas = {
      'class-loader': {
        title: '🔧 类加载器 (ClassLoader)',
        description:
          '负责加载.class文件到JVM内存中，包括Bootstrap、Extension、Application三级加载器，确保类的安全加载和命名空间隔离。',
      },
      'runtime-data': {
        title: '💾 运行时数据区 (Runtime Data Areas)',
        description:
          '包括方法区(存储类信息)、堆内存(对象存储)、栈内存(方法调用)、PC寄存器(程序计数器)、本地方法栈等内存区域。',
      },
      'execution-engine': {
        title: '⚡ 执行引擎 (Execution Engine)',
        description:
          '负责执行字节码，包括解释器(逐行解释)、JIT编译器(热点代码编译)、垃圾收集器(内存管理)等核心组件。',
      },
    }

    const selectArea = (area: string) => {
      selectedArea.value = area
    }

    return () =>
      h('div', { class: 'concept-demo' }, [
        h('h4', '🏗️ JVM架构组件'),
        h(
          'div',
          {
            class: 'jvm-areas',
            style:
              'display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin: 1rem 0;',
          },
          [
            Object.entries(areas).map(([key, area]) =>
              h(
                'div',
                {
                  class: 'jvm-area',
                  style: `padding: 1rem; border: 2px solid ${selectedArea.value === key ? '#667eea' : '#e9ecef'}; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; background: ${selectedArea.value === key ? '#f0f4ff' : 'white'};`,
                  onClick: () => selectArea(key),
                },
                [
                  h('h5', { style: 'margin: 0 0 0.5rem 0; color: #333;' }, area.title),
                  h(
                    'p',
                    { style: 'margin: 0; color: #666; font-size: 0.9rem; line-height: 1.4;' },
                    area.description,
                  ),
                ],
              ),
            ),
          ],
        ),
        selectedArea.value &&
          h(
            'div',
            {
              class: 'area-details',
              style:
                'margin-top: 1rem; padding: 1.5rem; background: #667eea; color: white; border-radius: 8px;',
            },
            [
              h(
                'h4',
                { style: 'margin: 0 0 1rem 0;' },
                `详细说明: ${areas[selectedArea.value as keyof typeof areas].title}`,
              ),
              h(
                'p',
                { style: 'margin: 0; line-height: 1.6;' },
                areas[selectedArea.value as keyof typeof areas].description,
              ),
            ],
          ),
      ])
  },
})

// 第1章专用组件 - var类型推断演示
const VarTypeInference = defineComponent({
  name: 'VarTypeInference',
  props: ['data'],
  setup(props) {
    const currentExample = ref(0)
    const examples = [
      {
        title: '基本类型推断',
        code: `// 传统方式
String message = "Hello, Java!";
List<String> names = new ArrayList<>();
Map<String, Integer> scores = new HashMap<>();

// 使用var简化
var message = "Hello, Java!";        // 推断为 String
var names = new ArrayList<String>(); // 推断为 ArrayList<String>
var scores = new HashMap<String, Integer>(); // 推断为 HashMap<String, Integer>`,
        explanation: 'var关键字让编译器根据初始化表达式自动推断变量类型，减少代码冗余',
      },
      {
        title: '复杂泛型简化',
        code: `// 传统方式 - 冗长的泛型声明
Map<String, List<Map<String, Object>>> complexData =
    new HashMap<String, List<Map<String, Object>>>();

// 使用var简化
var complexData = new HashMap<String, List<Map<String, Object>>>();

// 方法链调用
var result = someService.getData()
    .filter(item -> item.isValid())
    .map(item -> item.getName())
    .collect(Collectors.toList());`,
        explanation: '在复杂泛型和方法链场景中，var显著提升代码可读性',
      },
      {
        title: '使用限制示例',
        code: `// ❌ 错误用法
// var field;                    // 不能用于字段
// public void method(var param) // 不能用于参数
// var x;                        // 必须初始化
// var y = null;                 // 无法推断类型

// ✅ 正确用法
var text = "Hello";              // String
var number = 42;                 // int
var list = List.of(1, 2, 3);     // List<Integer>
var optional = Optional.empty(); // Optional<Object>`,
        explanation: 'var只能用于局部变量，且必须在声明时初始化',
      },
    ]

    const nextExample = () => {
      currentExample.value = (currentExample.value + 1) % examples.length
    }

    const prevExample = () => {
      currentExample.value =
        currentExample.value === 0 ? examples.length - 1 : currentExample.value - 1
    }

    return () =>
      h('div', { class: 'interactive-playground' }, [
        h('div', { class: 'demo-header' }, [
          h('h4', '🧠 var类型推断演示'),
          h(
            'span',
            { class: 'step-indicator' },
            `示例 ${currentExample.value + 1}/${examples.length}`,
          ),
        ]),
        h('h3', { style: 'color: #667eea; margin: 1rem 0;' }, examples[currentExample.value].title),
        h(
          'pre',
          {
            class: 'code-block',
            style:
              'background: #2d3748; color: #e2e8f0; padding: 1.5rem; border-radius: 8px; overflow-x: auto; line-height: 1.6;',
          },
          examples[currentExample.value].code,
        ),
        h(
          'div',
          {
            style:
              'background: #f0f4ff; padding: 1rem; border-radius: 6px; border-left: 4px solid #667eea; margin: 1rem 0;',
          },
          [
            h(
              'p',
              { style: 'margin: 0; color: #333; line-height: 1.6;' },
              examples[currentExample.value].explanation,
            ),
          ],
        ),
        h('div', { class: 'demo-controls' }, [
          h(
            'button',
            {
              onClick: prevExample,
              class: 'control-button',
            },
            '⬅️ 上一个示例',
          ),
          h(
            'button',
            {
              onClick: nextExample,
              class: 'control-button',
            },
            '下一个示例 ➡️',
          ),
        ]),
      ])
  },
})

// 第1章专用组件 - 集合工厂演示
const CollectionFactoryDemo = defineComponent({
  name: 'CollectionFactoryDemo',
  props: ['data'],
  setup(props) {
    const activeTab = ref(0)
    const examples = [
      {
        title: 'List.of() 示例',
        oldWay: `// 传统方式创建不可变列表
List<String> fruits = Collections.unmodifiableList(
    Arrays.asList("apple", "banana", "cherry")
);

// 或者使用 ArrayList
List<String> fruits = new ArrayList<>();
fruits.add("apple");
fruits.add("banana");
fruits.add("cherry");
fruits = Collections.unmodifiableList(fruits);`,
        newWay: `// Java 11 集合工厂方法
var fruits = List.of("apple", "banana", "cherry");

// 简洁、不可变、类型安全
System.out.println(fruits.size()); // 3
System.out.println(fruits.get(0)); // apple

// fruits.add("orange"); // 抛出 UnsupportedOperationException`,
        benefits: ['代码更简洁', '自动类型推断', '真正的不可变性', '更好的性能'],
      },
      {
        title: 'Set.of() 示例',
        oldWay: `// 传统方式创建不可变集合
Set<Integer> numbers = Collections.unmodifiableSet(
    new HashSet<>(Arrays.asList(1, 2, 3, 4, 5))
);

// 或者手动添加
Set<Integer> numbers = new HashSet<>();
numbers.add(1);
numbers.add(2);
numbers.add(3);
numbers = Collections.unmodifiableSet(numbers);`,
        newWay: `// Java 11 集合工厂方法
var numbers = Set.of(1, 2, 3, 4, 5);

// 自动去重、不可变
System.out.println(numbers.contains(3)); // true
System.out.println(numbers.size()); // 5

// numbers.add(6); // 抛出 UnsupportedOperationException`,
        benefits: ['自动去重', '不可变性保证', '内存优化', '线程安全'],
      },
      {
        title: 'Map.of() 示例',
        oldWay: `// 传统方式创建不可变映射
Map<String, Integer> config = new HashMap<>();
config.put("timeout", 30000);
config.put("retries", 3);
config.put("port", 8080);
config = Collections.unmodifiableMap(config);`,
        newWay: `// Java 11 集合工厂方法
var config = Map.of(
    "timeout", 30000,
    "retries", 3,
    "port", 8080
);

// 简洁的键值对定义
System.out.println(config.get("timeout")); // 30000

// config.put("ssl", true); // 抛出 UnsupportedOperationException`,
        benefits: ['键值对语法简洁', '编译时类型检查', '不可变性', '适合配置常量'],
      },
    ]

    const switchTab = (index: number) => {
      activeTab.value = index
    }

    return () =>
      h('div', { class: 'code-comparison' }, [
        h('h4', '🏭 集合工厂方法演示'),
        h('div', { class: 'example-tabs' }, [
          examples.map((example, index) =>
            h(
              'button',
              {
                class: ['tab-button', activeTab.value === index ? 'active' : ''],
                onClick: () => switchTab(index),
              },
              example.title,
            ),
          ),
        ]),
        h('div', { class: 'comparison-content' }, [
          h('div', { class: 'code-section' }, [
            h('h4', '❌ 传统方式'),
            h('pre', { class: 'code-block' }, examples[activeTab.value].oldWay),
          ]),
          h('div', { class: 'code-section' }, [
            h('h4', '✅ 集合工厂方法'),
            h('pre', { class: 'code-block' }, examples[activeTab.value].newWay),
          ]),
        ]),
        h(
          'div',
          {
            style: 'margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px;',
          },
          [
            h('h4', { style: 'margin: 0 0 0.5rem 0; color: #333;' }, '✨ 主要优势'),
            h('ul', { style: 'margin: 0; padding-left: 1.5rem;' }, [
              examples[activeTab.value].benefits.map((benefit) =>
                h('li', { style: 'color: #666; margin: 0.25rem 0;' }, benefit),
              ),
            ]),
          ],
        ),
      ])
  },
})

const getInteractiveComponent = (type: string) => {
  const componentMap: Record<string, any> = {
    // 通用组件
    'code-comparison': CodeComparison,
    'concept-demo': ConceptDemo,
    'interactive-playground': InteractivePlayground,

    // 第1章 - Java基础
    'compilation-demo': CompilationDemo,
    'bytecode-analysis': CodeComparison,
    'jvm-architecture': JVMArchitecture,
    'release-timeline': ConceptDemo,
    'lts-comparison': CodeComparison,
    'feature-lifecycle': ConceptDemo,
    'var-examples': CodeComparison,
    'type-inference': VarTypeInference,
    'best-practices': ConceptDemo,
    'evolution-timeline': ConceptDemo,
    'feature-states': ConceptDemo,
    'jep-process': ConceptDemo,
    'collection-factory-demo': CollectionFactoryDemo,
    'http-client-demo': InteractivePlayground,
    'string-methods-demo': CodeComparison,
    'knowledge-quiz': InteractivePlayground,
    'practice-exercises': InteractivePlayground,
    'chapter-review': ConceptDemo,

    // 第6章 - 并发编程
    'lock-demo': ConceptDemo,
    'atomic-demo': InteractivePlayground,
    'blocking-queue-demo': ConceptDemo,
    'future-demo': InteractivePlayground,
    'executor-demo': ConceptDemo,

    // 第7章 - 性能优化
    'memory-hierarchy-demo': ConceptDemo,
    'gc-process-demo': InteractivePlayground,
    'jit-demo': ConceptDemo,
    'jfr-recording-demo': InteractivePlayground,

    // 第8章 - JVM语言
    'language-comparison': CodeComparison,
    'pyramid-demo': ConceptDemo,
    'selection-checklist': InteractivePlayground,
    'bytecode-demo': CodeComparison,
    'risk-assessment': ConceptDemo,
    'interop-demo': CodeComparison,

    // 第9章 - Kotlin (已在 JavaChapter9.vue 中定义)
    'java-kotlin-converter': 'JavaKotlinConverter',
    'syntax-comparison': 'SyntaxComparison',
    'data-class-generator': 'DataClassGenerator',
    'null-safety-playground': 'NullSafetyPlayground',
    'coroutines-playground': 'CoroutinesPlayground',
  }

  return componentMap[type] || DefaultInteractive
}
</script>

<style scoped>
/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  max-width: 90vw;
  max-height: 90vh;
  width: 800px;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-body {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* 交互组件通用样式 */
.default-interactive {
  text-align: center;
  padding: 2rem;
}

.coming-soon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  color: #666;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.code-comparison {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.example-tabs {
  display: flex;
  gap: 0.5rem;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
}

.tab-button {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-button.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.comparison-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.code-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.code-section h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
}

.code-block {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
}

.concept-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.demo-header h4 {
  margin: 0;
  color: #333;
}

.step-indicator {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
}

.demo-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.control-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.control-button:hover:not(:disabled) {
  background: #5a6fd8;
}

.interactive-playground {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.code-editor {
  width: 100%;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  resize: vertical;
}

.run-button {
  background: #4caf50;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  align-self: flex-start;
}

.run-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result-section h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.result-output {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  margin: 0;
  min-height: 100px;
  white-space: pre-wrap;
}
</style>
